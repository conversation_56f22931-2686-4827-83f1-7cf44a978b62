<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schedule Appliance Repair Service | Queen City Appliance Repair Service | York, SC</title>
    <meta name="description" content="Schedule professional appliance repair service online. Fast, convenient booking for refrigerator, dishwasher, oven, washing machine, and dryer repair in York, SC and Charlotte area.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --service-blue: #005A9C;
            --clean-white: #FFFFFF;
            --action-orange: #F68D2E;
            --light-gray: #F5F5F5;
            --charcoal: #333333;
            --success-green: #28A745;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: var(--charcoal);
            background-color: var(--clean-white);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background-color: var(--service-blue);
            color: var(--clean-white);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .phone-cta {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s ease;
            display: inline-block;
        }

        .phone-cta:hover {
            background-color: #e67c1a;
        }

        /* Navigation */
        nav {
            background-color: var(--light-gray);
            padding: 0.5rem 0;
        }

        .nav-links {
            display: flex;
            list-style: none;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
        }

        .nav-links a {
            color: var(--charcoal);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--service-blue);
        }

        .nav-links .active {
            color: var(--service-blue);
            font-weight: bold;
        }

        /* Page Header */
        .page-header {
            background: linear-gradient(135deg, var(--service-blue) 0%, #0066b3 100%);
            color: var(--clean-white);
            padding: 3rem 0;
            text-align: center;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .page-header p {
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Form Section */
        .form-section {
            padding: 4rem 0;
        }

        .form-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: start;
        }

        .form-info h2 {
            color: var(--service-blue);
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }

        .form-info p {
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
            line-height: 1.7;
        }

        .info-list {
            list-style: none;
        }

        .info-list li {
            padding: 0.8rem 0;
            position: relative;
            padding-left: 2rem;
            font-size: 1.1rem;
        }

        .info-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--success-green);
            font-weight: bold;
            font-size: 1.2rem;
        }

        /* Form Styles */
        .service-form {
            background-color: var(--light-gray);
            padding: 2.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: var(--service-blue);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--service-blue);
        }

        .form-group textarea {
            height: 120px;
            resize: vertical;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .submit-button {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            min-height: 50px;
        }

        .submit-button:hover {
            background-color: #e67c1a;
            transform: translateY(-2px);
        }

        /* Contact Options */
        .contact-options {
            background-color: var(--light-gray);
            padding: 3rem 0;
            text-align: center;
        }

        .section-title {
            color: var(--service-blue);
            font-size: 2rem;
            margin-bottom: 2rem;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .contact-card {
            background-color: var(--clean-white);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .contact-icon {
            font-size: 3rem;
            color: var(--action-orange);
            margin-bottom: 1rem;
        }

        .contact-card h4 {
            color: var(--service-blue);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .cta-button {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            display: inline-block;
            transition: all 0.3s ease;
            margin-top: 1rem;
            min-height: 44px;
            line-height: 1.2;
        }

        .cta-button:hover {
            background-color: #e67c1a;
            transform: translateY(-2px);
        }

        /* Footer */
        footer {
            background-color: var(--charcoal);
            color: var(--clean-white);
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h4 {
            color: var(--action-orange);
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: var(--clean-white);
            text-decoration: none;
        }

        .footer-section a:hover {
            color: var(--action-orange);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #555;
            font-size: 0.9rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                gap: 1rem;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .form-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles */
        a:focus, button:focus, input:focus, select:focus, textarea:focus {
            outline: 2px solid var(--action-orange);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">Queen City Appliance Repair Service</div>
                <a href="tel:+***********" class="phone-cta" aria-label="Call us at ************">📞 (*************</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul class="nav-links">
                <li><a href="appliance_repair_homepage_v1.html">Home</a></li>
                <li><a href="appliance_repair_services_hub_v1.html">Services</a></li>
                <li><a href="appliance_repair_service_areas_v1.html">Service Areas</a></li>
                <li><a href="#schedule" class="active">Schedule Service</a></li>
                <li><a href="appliance_repair_why_choose_us_v1.html">Why Choose Us</a></li>
                <li><a href="appliance_repair_contact_us_v1.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section class="page-header">
            <div class="container">
                <h1>Schedule Your Appliance Repair Service</h1>
                <p>Fast, convenient online booking for professional appliance repair. Get your appliances working like new again.</p>
            </div>
        </section>

        <section class="form-section" id="schedule">
            <div class="container">
                <div class="form-container">
                    <div class="form-info">
                        <h2>Why Choose Our Service?</h2>
                        <p>When you schedule with Queen City Appliance Repair Service, you're choosing experienced professionals who understand the importance of getting your appliances working quickly and efficiently.</p>
                        <ul class="info-list">
                            <li>Same-day and next-day appointments available</li>
                            <li>Upfront pricing with no hidden fees</li>
                            <li>90-day warranty on all repairs</li>
                            <li>Expert technicians for all major brands</li>
                            <li>Emergency service available</li>
                            <li>Serving York, SC and Charlotte area</li>
                        </ul>
                        <p style="margin-top: 2rem;"><strong>Prefer to call?</strong> Our friendly staff is ready to help you schedule your appointment over the phone.</p>
                        <a href="tel:+***********" class="cta-button">Call (*************</a>
                    </div>

                    <form class="service-form" id="scheduleForm" action="#" method="POST">
                        <h3 style="color: var(--service-blue); margin-bottom: 1.5rem; font-size: 1.5rem;">Request Service Appointment</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="firstName">First Name *</label>
                                <input type="text" id="firstName" name="firstName" required>
                            </div>
                            <div class="form-group">
                                <label for="lastName">Last Name *</label>
                                <input type="text" id="lastName" name="lastName" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="phone">Phone Number *</label>
                                <input type="tel" id="phone" name="phone" required>
                            </div>
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" name="email">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="address">Service Address *</label>
                            <input type="text" id="address" name="address" placeholder="Street address, City, State, ZIP" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="applianceType">Appliance Type *</label>
                                <select id="applianceType" name="applianceType" required>
                                    <option value="">Select Appliance</option>
                                    <option value="refrigerator">Refrigerator/Freezer</option>
                                    <option value="dishwasher">Dishwasher</option>
                                    <option value="oven">Oven/Stove/Range</option>
                                    <option value="washer">Washing Machine</option>
                                    <option value="dryer">Dryer</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="brand">Brand</label>
                                <input type="text" id="brand" name="brand" placeholder="e.g., Whirlpool, Samsung, GE">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="preferredDate">Preferred Date</label>
                                <input type="date" id="preferredDate" name="preferredDate">
                            </div>
                            <div class="form-group">
                                <label for="preferredTime">Preferred Time</label>
                                <select id="preferredTime" name="preferredTime">
                                    <option value="">Any Time</option>
                                    <option value="morning">Morning (8 AM - 12 PM)</option>
                                    <option value="afternoon">Afternoon (12 PM - 5 PM)</option>
                                    <option value="evening">Evening (5 PM - 8 PM)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="problemDescription">Problem Description *</label>
                            <textarea id="problemDescription" name="problemDescription" placeholder="Please describe the issue you're experiencing with your appliance..." required></textarea>
                        </div>

                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="urgentRepair" name="urgentRepair" style="width: auto; margin-right: 10px;">
                                This is an urgent repair (same-day service requested)
                            </label>
                        </div>

                        <button type="submit" class="submit-button">Request Service Appointment</button>

                        <p style="font-size: 0.9rem; color: #666; margin-top: 1rem; text-align: center;">
                            * Required fields. We'll contact you within 2 hours to confirm your appointment.
                        </p>
                    </form>
                </div>
            </div>
        </section>

        <section class="contact-options">
            <div class="container">
                <h2 class="section-title">Other Ways to Schedule Service</h2>
                <div class="contact-grid">
                    <div class="contact-card">
                        <div class="contact-icon">📞</div>
                        <h4>Call Us</h4>
                        <p>Speak directly with our scheduling team for immediate assistance and same-day appointments.</p>
                        <a href="tel:+***********" class="cta-button">Call (*************</a>
                    </div>
                    <div class="contact-card">
                        <div class="contact-icon">📧</div>
                        <h4>Email Us</h4>
                        <p>Send us your service request details and we'll respond within 2 hours during business hours.</p>
                        <a href="mailto:<EMAIL>" class="cta-button">Email Service Request</a>
                    </div>
                    <div class="contact-card">
                        <div class="contact-icon">🚨</div>
                        <h4>Emergency Service</h4>
                        <p>For urgent appliance repairs that can't wait, call our emergency service line.</p>
                        <a href="tel:+***********" class="cta-button">Emergency Service</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Queen City Appliance Repair Service</h4>
                    <p>Expert Repairs, Reliable Service</p>
                    <p>Serving York, SC and the greater Charlotte area with professional appliance repair solutions.</p>
                </div>
                <div class="footer-section">
                    <h4>Our Services</h4>
                    <ul style="list-style: none;">
                        <li><a href="appliance_repair_refrigerator_repair_v1.html">Refrigerator Repair</a></li>
                        <li><a href="appliance_repair_dishwasher_repair_v1.html">Dishwasher Repair</a></li>
                        <li><a href="appliance_repair_oven_repair_v1.html">Oven & Range Repair</a></li>
                        <li><a href="appliance_repair_washing_machine_repair_v1.html">Washing Machine Repair</a></li>
                        <li><a href="appliance_repair_dryer_repair_v1.html">Dryer Repair</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Service Areas</h4>
                    <p><strong>South Carolina:</strong> Fort Mill, Tega Cay, Rock Hill, Lake Wylie, York, Clover, Newport</p>
                    <p><strong>North Carolina:</strong> Davidson, Weddington, Marvin, Huntersville, Cornelius, Waxhaw</p>
                </div>
                <div class="footer-section">
                    <h4>Contact Information</h4>
                    <p><strong>Phone:</strong> <a href="tel:+***********">(*************</a></p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <a href="appliance_repair_schedule_service_v1.html" class="cta-button" style="margin-top: 1rem; display: inline-block;">Schedule Service</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Queen City Appliance Repair Service. All rights reserved. | Professional appliance repair serving York, SC and Charlotte area.</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('scheduleForm');
            const submitButton = form.querySelector('.submit-button');

            // Set minimum date to today
            const dateInput = document.getElementById('preferredDate');
            const today = new Date().toISOString().split('T')[0];
            dateInput.setAttribute('min', today);

            // Form submission handler
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // Basic form validation
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.style.borderColor = '#dc3545';
                        isValid = false;
                    } else {
                        field.style.borderColor = '#ddd';
                    }
                });

                if (isValid) {
                    // Simulate form submission
                    submitButton.textContent = 'Submitting...';
                    submitButton.disabled = true;

                    setTimeout(() => {
                        alert('Thank you! Your service request has been submitted. We will contact you within 2 hours to confirm your appointment.');
                        form.reset();
                        submitButton.textContent = 'Request Service Appointment';
                        submitButton.disabled = false;
                    }, 1500);
                } else {
                    alert('Please fill in all required fields.');
                }
            });

            // Phone number formatting
            const phoneInput = document.getElementById('phone');
            phoneInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length >= 6) {
                    value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
                } else if (value.length >= 3) {
                    value = value.replace(/(\d{3})(\d{3})/, '($1) $2');
                }
                e.target.value = value;
            });

            // Add click tracking for CTA buttons
            const ctaButtons = document.querySelectorAll('.cta-button');
            ctaButtons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('CTA clicked:', this.textContent.trim());
                });
            });
        });

        // Add keyboard navigation support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });
    </script>
</body>
</html>
