<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us | Queen City Appliance Repair Service | York, SC & Charlotte Area</title>
    <meta name="description" content="Contact Queen City Appliance Repair Service for professional appliance repair in York, SC and Charlotte area. Phone, email, and service request options available.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --service-blue: #005A9C;
            --clean-white: #FFFFFF;
            --action-orange: #F68D2E;
            --light-gray: #F5F5F5;
            --charcoal: #333333;
            --success-green: #28A745;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: var(--charcoal);
            background-color: var(--clean-white);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background-color: var(--service-blue);
            color: var(--clean-white);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .phone-cta {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s ease;
            display: inline-block;
        }

        .phone-cta:hover {
            background-color: #e67c1a;
        }

        /* Navigation */
        nav {
            background-color: var(--light-gray);
            padding: 0.5rem 0;
        }

        .nav-links {
            display: flex;
            list-style: none;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
        }

        .nav-links a {
            color: var(--charcoal);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--service-blue);
        }

        .nav-links .active {
            color: var(--service-blue);
            font-weight: bold;
        }

        /* Page Header */
        .page-header {
            background: linear-gradient(135deg, var(--service-blue) 0%, #0066b3 100%);
            color: var(--clean-white);
            padding: 3rem 0;
            text-align: center;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .page-header p {
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Contact Section */
        .contact-section {
            padding: 4rem 0;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-bottom: 4rem;
        }

        .contact-card {
            background-color: var(--light-gray);
            padding: 2.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .contact-icon {
            font-size: 3rem;
            color: var(--action-orange);
            margin-bottom: 1rem;
        }

        .contact-card h3 {
            color: var(--service-blue);
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .contact-card p {
            margin-bottom: 1.5rem;
            font-size: 1.1rem;
        }

        .contact-info {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--service-blue);
            margin-bottom: 1rem;
        }

        .cta-button {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            display: inline-block;
            transition: all 0.3s ease;
            min-height: 44px;
            line-height: 1.2;
        }

        .cta-button:hover {
            background-color: #e67c1a;
            transform: translateY(-2px);
        }

        /* Contact Form */
        .form-section {
            background-color: var(--light-gray);
            padding: 4rem 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.2rem;
            margin-bottom: 3rem;
            color: var(--service-blue);
        }

        .contact-form {
            max-width: 600px;
            margin: 0 auto;
            background-color: var(--clean-white);
            padding: 2.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: var(--service-blue);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--service-blue);
        }

        .form-group textarea {
            height: 120px;
            resize: vertical;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .submit-button {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            min-height: 50px;
        }

        .submit-button:hover {
            background-color: #e67c1a;
            transform: translateY(-2px);
        }

        /* Service Areas */
        .service-areas-section {
            padding: 4rem 0;
        }

        .areas-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .area-column {
            text-align: center;
            padding: 2rem;
        }

        .area-column h4 {
            color: var(--service-blue);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .area-list {
            list-style: none;
        }

        .area-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--light-gray);
        }

        /* Footer */
        footer {
            background-color: var(--charcoal);
            color: var(--clean-white);
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h4 {
            color: var(--action-orange);
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: var(--clean-white);
            text-decoration: none;
        }

        .footer-section a:hover {
            color: var(--action-orange);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #555;
            font-size: 0.9rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                gap: 1rem;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .contact-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles */
        a:focus, button:focus, input:focus, select:focus, textarea:focus {
            outline: 2px solid var(--action-orange);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">Queen City Appliance Repair Service</div>
                <a href="tel:+***********" class="phone-cta" aria-label="Call us at ************">📞 (*************</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul class="nav-links">
                <li><a href="appliance_repair_homepage_v1.html">Home</a></li>
                <li><a href="appliance_repair_services_hub_v1.html">Services</a></li>
                <li><a href="appliance_repair_service_areas_v1.html">Service Areas</a></li>
                <li><a href="appliance_repair_schedule_service_v1.html">Schedule Service</a></li>
                <li><a href="appliance_repair_why_choose_us_v1.html">Why Choose Us</a></li>
                <li><a href="#contact" class="active">Contact</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section class="page-header">
            <div class="container">
                <h1>Contact Queen City Appliance Repair Service</h1>
                <p>Get in touch with our professional appliance repair team. We're here to help with all your appliance service needs.</p>
            </div>
        </section>

        <section class="contact-section" id="contact">
            <div class="container">
                <div class="contact-grid">
                    <div class="contact-card">
                        <div class="contact-icon">📞</div>
                        <h3>Call Us</h3>
                        <p>Speak directly with our friendly team for immediate assistance, scheduling, or emergency service.</p>
                        <div class="contact-info">(*************</div>
                        <p><strong>Business Hours:</strong><br>
                        Monday - Friday: 8:00 AM - 6:00 PM<br>
                        Saturday: 9:00 AM - 4:00 PM<br>
                        Sunday: Emergency Service Only</p>
                        <a href="tel:+***********" class="cta-button">Call Now</a>
                    </div>

                    <div class="contact-card">
                        <div class="contact-icon">📧</div>
                        <h3>Email Us</h3>
                        <p>Send us your service request or questions and we'll respond within 2 hours during business hours.</p>
                        <div class="contact-info"><EMAIL></div>
                        <p><strong>Response Time:</strong><br>
                        Business Hours: Within 2 hours<br>
                        After Hours: Next business day<br>
                        Emergency: Call for immediate service</p>
                        <a href="mailto:<EMAIL>" class="cta-button">Send Email</a>
                    </div>

                    <div class="contact-card">
                        <div class="contact-icon">🚨</div>
                        <h3>Emergency Service</h3>
                        <p>For urgent appliance repairs that can't wait, our emergency service is available 24/7.</p>
                        <div class="contact-info">Emergency Line: (*************</div>
                        <p><strong>Emergency Situations:</strong><br>
                        • Refrigerator not cooling<br>
                        • Gas appliance safety issues<br>
                        • Water leaks from appliances<br>
                        • Complete appliance failure</p>
                        <a href="tel:+***********" class="cta-button">Emergency Service</a>
                    </div>
                </div>
            </div>
        </section>

        <section class="form-section">
            <div class="container">
                <h2 class="section-title">Send Us a Message</h2>
                <form class="contact-form" id="contactForm" action="#" method="POST">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">First Name *</label>
                            <input type="text" id="firstName" name="firstName" required>
                        </div>
                        <div class="form-group">
                            <label for="lastName">Last Name *</label>
                            <input type="text" id="lastName" name="lastName" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone">Phone Number *</label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <select id="subject" name="subject">
                            <option value="">Select a topic</option>
                            <option value="service-request">Service Request</option>
                            <option value="emergency">Emergency Service</option>
                            <option value="question">General Question</option>
                            <option value="feedback">Feedback</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="message">Message *</label>
                        <textarea id="message" name="message" placeholder="Please tell us how we can help you..." required></textarea>
                    </div>

                    <button type="submit" class="submit-button">Send Message</button>

                    <p style="font-size: 0.9rem; color: #666; margin-top: 1rem; text-align: center;">
                        * Required fields. We'll respond within 2 hours during business hours.
                    </p>
                </form>
            </div>
        </section>

        <section class="service-areas-section">
            <div class="container">
                <h2 class="section-title">Service Areas</h2>
                <p style="text-align: center; font-size: 1.1rem; margin-bottom: 2rem;">We proudly serve communities throughout York, SC and the greater Charlotte area</p>
                <div class="areas-grid">
                    <div class="area-column">
                        <h4>South Carolina</h4>
                        <ul class="area-list">
                            <li>Fort Mill</li>
                            <li>Tega Cay</li>
                            <li>Rock Hill</li>
                            <li>Lake Wylie</li>
                            <li>York</li>
                            <li>Clover</li>
                            <li>Newport</li>
                        </ul>
                    </div>
                    <div class="area-column">
                        <h4>North Carolina</h4>
                        <ul class="area-list">
                            <li>Davidson</li>
                            <li>Weddington</li>
                            <li>Marvin</li>
                            <li>Huntersville</li>
                            <li>Cornelius</li>
                            <li>Waxhaw</li>
                        </ul>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 2rem;">
                    <p><strong>Don't see your city listed?</strong> Contact us to check if we service your area.</p>
                    <a href="appliance_repair_schedule_service_v1.html" class="cta-button" style="margin-top: 1rem;">Schedule Service</a>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Queen City Appliance Repair Service</h4>
                    <p>Expert Repairs, Reliable Service</p>
                    <p>Serving York, SC and the greater Charlotte area with professional appliance repair solutions.</p>
                </div>
                <div class="footer-section">
                    <h4>Our Services</h4>
                    <ul style="list-style: none;">
                        <li><a href="appliance_repair_refrigerator_repair_v1.html">Refrigerator Repair</a></li>
                        <li><a href="appliance_repair_dishwasher_repair_v1.html">Dishwasher Repair</a></li>
                        <li><a href="appliance_repair_oven_repair_v1.html">Oven & Range Repair</a></li>
                        <li><a href="appliance_repair_washing_machine_repair_v1.html">Washing Machine Repair</a></li>
                        <li><a href="appliance_repair_dryer_repair_v1.html">Dryer Repair</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Service Areas</h4>
                    <p><strong>South Carolina:</strong> Fort Mill, Tega Cay, Rock Hill, Lake Wylie, York, Clover, Newport</p>
                    <p><strong>North Carolina:</strong> Davidson, Weddington, Marvin, Huntersville, Cornelius, Waxhaw</p>
                </div>
                <div class="footer-section">
                    <h4>Contact Information</h4>
                    <p><strong>Phone:</strong> <a href="tel:+***********">(*************</a></p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <a href="appliance_repair_schedule_service_v1.html" class="cta-button" style="margin-top: 1rem; display: inline-block;">Schedule Service</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Queen City Appliance Repair Service. All rights reserved. | Professional appliance repair serving York, SC and Charlotte area.</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('contactForm');
            const submitButton = form.querySelector('.submit-button');

            // Form submission handler
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // Basic form validation
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.style.borderColor = '#dc3545';
                        isValid = false;
                    } else {
                        field.style.borderColor = '#ddd';
                    }
                });

                if (isValid) {
                    // Simulate form submission
                    submitButton.textContent = 'Sending...';
                    submitButton.disabled = true;

                    setTimeout(() => {
                        alert('Thank you! Your message has been sent. We will respond within 2 hours during business hours.');
                        form.reset();
                        submitButton.textContent = 'Send Message';
                        submitButton.disabled = false;
                    }, 1500);
                } else {
                    alert('Please fill in all required fields.');
                }
            });

            // Phone number formatting
            const phoneInput = document.getElementById('phone');
            phoneInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length >= 6) {
                    value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
                } else if (value.length >= 3) {
                    value = value.replace(/(\d{3})(\d{3})/, '($1) $2');
                }
                e.target.value = value;
            });

            // Add click tracking for CTA buttons
            const ctaButtons = document.querySelectorAll('.cta-button');
            ctaButtons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('CTA clicked:', this.textContent.trim());
                });
            });
        });

        // Add keyboard navigation support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });
    </script>
</body>
</html>
