<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Refrigerator & Freezer Repair | Queen City Appliance Repair Service | York, SC</title>
    <meta name="description" content="Professional refrigerator and freezer repair service in York, SC and Charlotte area. Expert diagnosis and repair for cooling issues, ice makers, and temperature control problems.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --service-blue: #005A9C;
            --clean-white: #FFFFFF;
            --action-orange: #F68D2E;
            --light-gray: #F5F5F5;
            --charcoal: #333333;
            --success-green: #28A745;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: var(--charcoal);
            background-color: var(--clean-white);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background-color: var(--service-blue);
            color: var(--clean-white);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .phone-cta {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s ease;
            display: inline-block;
        }

        .phone-cta:hover {
            background-color: #e67c1a;
        }

        /* Navigation */
        nav {
            background-color: var(--light-gray);
            padding: 0.5rem 0;
        }

        .nav-links {
            display: flex;
            list-style: none;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
        }

        .nav-links a {
            color: var(--charcoal);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--service-blue);
        }

        /* Page Header */
        .page-header {
            background: linear-gradient(135deg, var(--service-blue) 0%, #0066b3 100%);
            color: var(--clean-white);
            padding: 3rem 0;
            text-align: center;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .page-header p {
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto 2rem;
        }

        .header-cta {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Service Overview */
        .service-overview {
            padding: 4rem 0;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .overview-content h2 {
            color: var(--service-blue);
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }

        .overview-content p {
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
            line-height: 1.7;
        }

        .overview-features {
            list-style: none;
        }

        .overview-features li {
            padding: 0.8rem 0;
            position: relative;
            padding-left: 2rem;
            font-size: 1.1rem;
        }

        .overview-features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--success-green);
            font-weight: bold;
            font-size: 1.2rem;
        }

        .overview-image {
            background: linear-gradient(45deg, var(--service-blue), var(--action-orange));
            height: 300px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 6rem;
            color: var(--clean-white);
        }

        /* Problems Section */
        .problems-section {
            background-color: var(--light-gray);
            padding: 4rem 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.2rem;
            margin-bottom: 3rem;
            color: var(--service-blue);
        }

        .problems-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .problem-card {
            background-color: var(--clean-white);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid var(--action-orange);
        }

        .problem-card h4 {
            color: var(--service-blue);
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }

        .problem-card p {
            margin-bottom: 1rem;
        }

        .problem-symptoms {
            list-style: none;
        }

        .problem-symptoms li {
            padding: 0.3rem 0;
            position: relative;
            padding-left: 1.5rem;
            font-size: 0.95rem;
            color: #666;
        }

        .problem-symptoms li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: var(--action-orange);
        }

        /* CTA Buttons */
        .cta-button {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-size: 1.1rem;
            font-weight: bold;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0 10px 10px 0;
            min-height: 44px;
            line-height: 1.2;
            text-align: center;
        }

        .cta-button:hover {
            background-color: #e67c1a;
            transform: translateY(-2px);
        }

        .cta-secondary {
            background-color: transparent;
            border: 2px solid var(--service-blue);
            color: var(--service-blue);
        }

        .cta-secondary:hover {
            background-color: var(--service-blue);
            color: var(--clean-white);
        }

        .cta-white {
            background-color: transparent;
            border: 2px solid var(--clean-white);
            color: var(--clean-white);
        }

        .cta-white:hover {
            background-color: var(--clean-white);
            color: var(--service-blue);
        }

        /* Process Section */
        .process-section {
            padding: 4rem 0;
        }

        .process-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .process-step {
            text-align: center;
            padding: 2rem;
        }

        .step-number {
            background-color: var(--action-orange);
            color: var(--clean-white);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0 auto 1rem;
        }

        .process-step h4 {
            color: var(--service-blue);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        /* Emergency CTA */
        .emergency-cta {
            background-color: var(--service-blue);
            color: var(--clean-white);
            padding: 3rem 0;
            text-align: center;
        }

        .emergency-cta h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .emergency-cta p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Footer */
        footer {
            background-color: var(--charcoal);
            color: var(--clean-white);
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h4 {
            color: var(--action-orange);
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: var(--clean-white);
            text-decoration: none;
        }

        .footer-section a:hover {
            color: var(--action-orange);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #555;
            font-size: 0.9rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                gap: 1rem;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .overview-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .header-cta {
                flex-direction: column;
                align-items: center;
            }

            .cta-button {
                width: 100%;
                max-width: 300px;
            }
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles */
        a:focus, button:focus {
            outline: 2px solid var(--action-orange);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">Queen City Appliance Repair Service</div>
                <a href="tel:+18035551234" class="phone-cta" aria-label="Call us at ************">📞 (*************</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul class="nav-links">
                <li><a href="appliance_repair_homepage_v1.html">Home</a></li>
                <li><a href="appliance_repair_services_hub_v1.html">Services</a></li>
                <li><a href="appliance_repair_service_areas_v1.html">Service Areas</a></li>
                <li><a href="appliance_repair_schedule_service_v1.html">Schedule Service</a></li>
                <li><a href="appliance_repair_why_choose_us_v1.html">Why Choose Us</a></li>
                <li><a href="appliance_repair_contact_us_v1.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section class="page-header">
            <div class="container">
                <h1>Refrigerator & Freezer Repair Service</h1>
                <p>Expert diagnosis and repair for all refrigerator and freezer issues. From cooling problems to ice maker malfunctions, we restore your appliance to peak performance.</p>
                <div class="header-cta">
                    <a href="appliance_repair_schedule_service_v1.html" class="cta-button">Schedule Repair Service</a>
                    <a href="tel:+18035551234" class="cta-button cta-white">Call (*************</a>
                </div>
            </div>
        </section>

        <section class="service-overview">
            <div class="container">
                <div class="overview-grid">
                    <div class="overview-content">
                        <h2>Professional Refrigerator Repair You Can Trust</h2>
                        <p>When your refrigerator stops working properly, it's more than an inconvenience—it's a threat to your food safety and family's well-being. Our experienced technicians provide fast, reliable repair service for all refrigerator and freezer issues.</p>
                        <p>We service all major brands and models, from basic top-freezer units to high-end French door refrigerators with advanced features.</p>
                        <ul class="overview-features">
                            <li>Same-day and emergency repair service available</li>
                            <li>Expert diagnosis of cooling and temperature issues</li>
                            <li>Ice maker and water dispenser repair</li>
                            <li>Compressor and cooling system service</li>
                            <li>Door seal and gasket replacement</li>
                            <li>90-day warranty on all repairs</li>
                        </ul>
                    </div>
                    <div class="overview-image">❄️</div>
                </div>
            </div>
        </section>

        <section class="problems-section">
            <div class="container">
                <h2 class="section-title">Common Refrigerator Problems We Fix</h2>
                <div class="problems-grid">
                    <div class="problem-card">
                        <h4>Not Cooling Properly</h4>
                        <p>Temperature control issues can lead to food spoilage and safety concerns.</p>
                        <ul class="problem-symptoms">
                            <li>Food spoiling quickly</li>
                            <li>Ice cream too soft</li>
                            <li>Inconsistent temperatures</li>
                            <li>Freezer not freezing</li>
                        </ul>
                    </div>

                    <div class="problem-card">
                        <h4>Ice Maker Problems</h4>
                        <p>Ice maker malfunctions are among the most common refrigerator issues.</p>
                        <ul class="problem-symptoms">
                            <li>No ice production</li>
                            <li>Small or hollow ice cubes</li>
                            <li>Ice tastes bad or smells</li>
                            <li>Ice maker making noise</li>
                        </ul>
                    </div>

                    <div class="problem-card">
                        <h4>Water Leaks</h4>
                        <p>Water leaks can damage your floors and indicate serious internal problems.</p>
                        <ul class="problem-symptoms">
                            <li>Water pooling under refrigerator</li>
                            <li>Water inside refrigerator</li>
                            <li>Leaking from water dispenser</li>
                            <li>Clogged drain line</li>
                        </ul>
                    </div>

                    <div class="problem-card">
                        <h4>Strange Noises</h4>
                        <p>Unusual sounds often indicate mechanical problems that need attention.</p>
                        <ul class="problem-symptoms">
                            <li>Loud humming or buzzing</li>
                            <li>Clicking or rattling sounds</li>
                            <li>Fan motor noise</li>
                            <li>Compressor issues</li>
                        </ul>
                    </div>

                    <div class="problem-card">
                        <h4>Door Seal Issues</h4>
                        <p>Damaged door seals waste energy and affect cooling performance.</p>
                        <ul class="problem-symptoms">
                            <li>Warm air entering refrigerator</li>
                            <li>Condensation on exterior</li>
                            <li>High energy bills</li>
                            <li>Door not sealing properly</li>
                        </ul>
                    </div>

                    <div class="problem-card">
                        <h4>Electrical Problems</h4>
                        <p>Control panel and electrical issues require professional diagnosis.</p>
                        <ul class="problem-symptoms">
                            <li>Display not working</li>
                            <li>Temperature controls unresponsive</li>
                            <li>Lights not working</li>
                            <li>Refrigerator not turning on</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section class="process-section">
            <div class="container">
                <h2 class="section-title">Our Repair Process</h2>
                <div class="process-steps">
                    <div class="process-step">
                        <div class="step-number">1</div>
                        <h4>Schedule Service</h4>
                        <p>Call us or schedule online. We offer same-day appointments for urgent repairs.</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">2</div>
                        <h4>Expert Diagnosis</h4>
                        <p>Our technician performs a thorough inspection to identify the root cause of the problem.</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">3</div>
                        <h4>Transparent Pricing</h4>
                        <p>We provide upfront pricing before any work begins. No hidden fees or surprises.</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">4</div>
                        <h4>Professional Repair</h4>
                        <p>Using genuine parts and expert techniques, we restore your refrigerator to optimal performance.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="emergency-cta">
            <div class="container">
                <h2>Refrigerator Emergency? We're Here to Help</h2>
                <p>Don't let a broken refrigerator spoil your food or disrupt your family's routine. Contact Queen City Appliance Repair Service for fast, professional repair service.</p>
                <a href="appliance_repair_schedule_service_v1.html" class="cta-button">Schedule Service Now</a>
                <a href="tel:+18035551234" class="cta-button cta-white">Emergency Service: (*************</a>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Queen City Appliance Repair Service</h4>
                    <p>Expert Repairs, Reliable Service</p>
                    <p>Serving York, SC and the greater Charlotte area with professional appliance repair solutions.</p>
                </div>
                <div class="footer-section">
                    <h4>Our Services</h4>
                    <ul style="list-style: none;">
                        <li><a href="appliance_repair_refrigerator_repair_v1.html">Refrigerator Repair</a></li>
                        <li><a href="appliance_repair_dishwasher_repair_v1.html">Dishwasher Repair</a></li>
                        <li><a href="appliance_repair_oven_repair_v1.html">Oven & Range Repair</a></li>
                        <li><a href="appliance_repair_washing_machine_repair_v1.html">Washing Machine Repair</a></li>
                        <li><a href="appliance_repair_dryer_repair_v1.html">Dryer Repair</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Service Areas</h4>
                    <p><strong>South Carolina:</strong> Fort Mill, Tega Cay, Rock Hill, Lake Wylie, York, Clover, Newport</p>
                    <p><strong>North Carolina:</strong> Davidson, Weddington, Marvin, Huntersville, Cornelius, Waxhaw</p>
                </div>
                <div class="footer-section">
                    <h4>Contact Information</h4>
                    <p><strong>Phone:</strong> <a href="tel:+18035551234">(*************</a></p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <a href="appliance_repair_schedule_service_v1.html" class="cta-button" style="margin-top: 1rem; display: inline-block;">Schedule Service</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Queen City Appliance Repair Service. All rights reserved. | Professional appliance repair serving York, SC and Charlotte area.</p>
            </div>
        </div>
    </footer>

    <script>
        // Add smooth scrolling for anchor links
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add click tracking for CTA buttons
            const ctaButtons = document.querySelectorAll('.cta-button');
            ctaButtons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('CTA clicked:', this.textContent.trim());
                });
            });
        });

        // Add keyboard navigation support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });
    </script>
</body>
</html>
