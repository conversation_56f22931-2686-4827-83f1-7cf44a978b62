Appliance Repair Website - Complete Page Generation (3-Wave Model)
Core Objective
Your primary task is to generate a complete, professional website for an appliance repair business. The process will follow a 3-wave development model. In Wave 1, you will generate all essential pages. In Waves 2 and 3, you will execute targeted improvements based on the specific evaluation criteria provided below. Each wave must be executed independently.

Mandatory Constraints & Restrictions
You must strictly adhere to the following restrictions throughout all generated content:

DO NOT USE the following words or phrases: "Free estimates," "licensed," "insured," "bonded."

AVOID any language that implies low or cheap rates, such as "low rates," "cheap rates," "affordable," "discount," etc. Instead, focus on value, expertise, and transparency.

DO NOT mention or offer services for small appliances like toasters, blenders, or microwaves. The focus is exclusively on major household appliances.

Brand Information
1. Business Identity
Business Name: Queen City Appliance Repair Service

Tagline: "Expert Repairs, Reliable Service" (customizable)

Core Values: Reliability, Expertise, Timeliness, Honesty, Professionalism

Unique Selling Proposition: Prompt, professional repair service for all major appliance brands, proudly serving communities in and around York, SC, and the greater Charlotte area.

2. Service Area
The website must clearly state that service is provided to the following locations:

South Carolina: Fort Mill, Tega Cay, Rock Hill, Lake Wylie, York, Clover, Newport

North Carolina: Davidson, Weddington, Marvin, Huntersville, Cornelius, Waxhaw

3. Visual Brand Guidelines
Primary Colors:

Service Blue: #005A9C (professionalism, trust)

Clean White: #FFFFFF (clarity, order)

Action Orange: #F68D2E (calls to action, service)

Secondary Colors:

Light Gray: #F5F5F5 (backgrounds)

Charcoal: #333333 (text)

Success Green: #28A745 (confirmation messages, warranty info)

Typography:

Headers: Modern, strong sans-serif (e.g., Inter, Montserrat, or system fonts for performance).

Body: Clean, highly readable sans-serif.

Disclaimers/Warranty: Smaller, clear serif.

Imagery Style:

Bright, professional photography.

Clean-cut technicians in uniform.

Service vans with clear branding.

Close-ups of appliance parts or tools in a clean environment.

Images of fully repaired and functioning appliances in a home setting.

Complete Page Set (15 Total Pages)
Core Pages (4 pages)
Homepage: Main entry point with an overview of services, service areas, and trust signals.

Services Hub: A central page that links to each specific appliance repair service page.

Service Areas: A detailed page listing all towns served, potentially with an embedded map.

Blog/Resources: Hub for articles like "5 Signs Your Refrigerator Needs Repair" or "How to Maintain Your Washing Machine."

Service Pages (6 pages)
Refrigerator & Freezer Repair: Service for all common refrigerator and freezer issues.

Dishwasher Repair: Solutions for leaks, cleaning issues, and mechanical failures.

Oven, Stove & Range Repair: Covering gas and electric models, heating issues, and control malfunctions.

Washing Machine Repair: Addressing leaks, spin cycle problems, and electronic errors.

Dryer Repair: Service for heating problems, drum issues, and both gas and electric models.

Brands We Service: A page listing logos of major brands serviced (e.g., Whirlpool, Maytag, Samsung, LG, GE, Kenmore, etc.).

Business & Conversion Pages (5 pages)
Schedule Service: A primary conversion page with a form to request an appointment.

Pricing & Warranty: A page explaining the pricing model (e.g., service call fee + parts/labor) and details of the service warranty.

Why Choose Us: Outlines the company's process, commitment to quality, technician expertise, and customer service approach.

Customer Reviews: A dedicated page for patient testimonials and success stories.

Contact Us: Direct contact information, including phone number, email, and a contact form for general inquiries.

3-Wave Execution Model
Wave 1: Complete Website Generation
Generate all 15 pages with full HTML, CSS, and JavaScript functionality.

Output: appliance_repair_[pagetype]_v1.html for each page

Requirements: Semantic HTML, inline/embedded CSS for responsive design, JS for interactions, mobile-first, accessibility features.

Wave 2: First Improvement Iteration
Evaluate Wave 1 output and improve based on the specific criteria below.

Output: appliance_repair_[pagetype]_v2.html for each page

Wave 3: Final Refinement
Final evaluation and polish based on Wave 2 results.

Output: appliance_repair_[pagetype]_v3.html for each page

Specific Evaluation Criteria (for Waves 2 & 3)
1. Conversion Elements Analysis
CTA Placement: High visibility, logical frequency, thumb-zone placement on mobile.

CTA Design: Optimal button size (min 44x44px), strong color contrast, clear hover/active states.

CTA Copy: Use strong action verbs (e.g., "Schedule Your Repair," "Request a Technician").

Form Fields: Minimize fields for initial contact (e.g., Name, Phone, Appliance Type, Issue Description).

Trust Signals: Prominently display customer testimonials, years of experience, and warranty information near CTAs.

Phone Numbers: Must be click-to-call and highly visible.

2. Content & Copy Evaluation
Headline Hierarchy: Clear H1-H6 flow, use keywords like "Refrigerator Repair in Rock Hill, SC."

Value Propositions: Clearly state benefits (e.g., "Fast, reliable service to get your home running smoothly").

Scanability: Use short paragraphs, bullet points for symptoms/benefits, and bold text for key terms.

Service Descriptions: Clearly define the problem (e.g., "Is your dryer not heating?"), present the solution, and focus on the outcome (a working appliance).

FAQ Completeness: Address common questions about repair process, timing, cost structure, and warranty.

3. Visual Hierarchy & Technical Performance
Visual Flow: Guide the user's eye toward CTAs using F/Z-patterns.

Contrast & White Space: Ensure text is readable and the layout feels uncluttered.

Image Placement: Use images to support service descriptions without disrupting the flow. Implement lazy loading.

Load Time: Optimize for speed (<3s TTI) by compressing images (WebP) and minifying code.

Mobile Performance: Ensure a flawless experience on mobile devices, with correctly sized touch targets and no horizontal scroll.

4. Accessibility Compliance
ARIA Labels: Use for forms, buttons, and dynamic content.

Keyboard Navigation: Logical tab order and visible focus indicators.

Screen Reader: Semantic HTML and descriptive alt text for all images.

Color Independence: Information should not be conveyed by color alone.

File Naming Convention
Wave 1: appliance_repair_[pagename]_v1.html

Wave 2: appliance_repair_[pagename]_v2.html

Wave 3: appliance_repair_[pagename]_v3.html

Examples: appliance_repair_homepage_v1.html, appliance_repair_refrigerator_repair_v2.html