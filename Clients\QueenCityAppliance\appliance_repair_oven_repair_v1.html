<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Oven, Stove & Range Repair | Queen City Appliance Repair Service | York, SC</title>
    <meta name="description" content="Professional oven, stove, and range repair service in York, SC and Charlotte area. Expert service for gas and electric models, heating issues, and control malfunctions.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --service-blue: #005A9C;
            --clean-white: #FFFFFF;
            --action-orange: #F68D2E;
            --light-gray: #F5F5F5;
            --charcoal: #333333;
            --success-green: #28A745;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: var(--charcoal);
            background-color: var(--clean-white);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background-color: var(--service-blue);
            color: var(--clean-white);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .phone-cta {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s ease;
            display: inline-block;
        }

        .phone-cta:hover {
            background-color: #e67c1a;
        }

        /* Navigation */
        nav {
            background-color: var(--light-gray);
            padding: 0.5rem 0;
        }

        .nav-links {
            display: flex;
            list-style: none;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
        }

        .nav-links a {
            color: var(--charcoal);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--service-blue);
        }

        /* Page Header */
        .page-header {
            background: linear-gradient(135deg, var(--service-blue) 0%, #0066b3 100%);
            color: var(--clean-white);
            padding: 3rem 0;
            text-align: center;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .page-header p {
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto 2rem;
        }

        .header-cta {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Service Overview */
        .service-overview {
            padding: 4rem 0;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .overview-content h2 {
            color: var(--service-blue);
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }

        .overview-content p {
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
            line-height: 1.7;
        }

        .overview-features {
            list-style: none;
        }

        .overview-features li {
            padding: 0.8rem 0;
            position: relative;
            padding-left: 2rem;
            font-size: 1.1rem;
        }

        .overview-features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--success-green);
            font-weight: bold;
            font-size: 1.2rem;
        }

        .overview-image {
            background: linear-gradient(45deg, var(--service-blue), var(--action-orange));
            height: 300px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 6rem;
            color: var(--clean-white);
        }

        /* Problems Section */
        .problems-section {
            background-color: var(--light-gray);
            padding: 4rem 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.2rem;
            margin-bottom: 3rem;
            color: var(--service-blue);
        }

        .problems-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .problem-card {
            background-color: var(--clean-white);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid var(--action-orange);
        }

        .problem-card h4 {
            color: var(--service-blue);
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }

        .problem-card p {
            margin-bottom: 1rem;
        }

        .problem-symptoms {
            list-style: none;
        }

        .problem-symptoms li {
            padding: 0.3rem 0;
            position: relative;
            padding-left: 1.5rem;
            font-size: 0.95rem;
            color: #666;
        }

        .problem-symptoms li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: var(--action-orange);
        }

        /* CTA Buttons */
        .cta-button {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-size: 1.1rem;
            font-weight: bold;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0 10px 10px 0;
            min-height: 44px;
            line-height: 1.2;
            text-align: center;
        }

        .cta-button:hover {
            background-color: #e67c1a;
            transform: translateY(-2px);
        }

        .cta-white {
            background-color: transparent;
            border: 2px solid var(--clean-white);
            color: var(--clean-white);
        }

        .cta-white:hover {
            background-color: var(--clean-white);
            color: var(--service-blue);
        }

        /* Emergency CTA */
        .emergency-cta {
            background-color: var(--service-blue);
            color: var(--clean-white);
            padding: 3rem 0;
            text-align: center;
        }

        .emergency-cta h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .emergency-cta p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Footer */
        footer {
            background-color: var(--charcoal);
            color: var(--clean-white);
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h4 {
            color: var(--action-orange);
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: var(--clean-white);
            text-decoration: none;
        }

        .footer-section a:hover {
            color: var(--action-orange);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #555;
            font-size: 0.9rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                gap: 1rem;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .overview-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .header-cta {
                flex-direction: column;
                align-items: center;
            }

            .cta-button {
                width: 100%;
                max-width: 300px;
            }
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles */
        a:focus, button:focus {
            outline: 2px solid var(--action-orange);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">Queen City Appliance Repair Service</div>
                <a href="tel:+18035551234" class="phone-cta" aria-label="Call us at ************">📞 (*************</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul class="nav-links">
                <li><a href="appliance_repair_homepage_v1.html">Home</a></li>
                <li><a href="appliance_repair_services_hub_v1.html">Services</a></li>
                <li><a href="appliance_repair_service_areas_v1.html">Service Areas</a></li>
                <li><a href="appliance_repair_schedule_service_v1.html">Schedule Service</a></li>
                <li><a href="appliance_repair_why_choose_us_v1.html">Why Choose Us</a></li>
                <li><a href="appliance_repair_contact_us_v1.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section class="page-header">
            <div class="container">
                <h1>Oven, Stove & Range Repair Service</h1>
                <p>Professional service for gas and electric ovens, stoves, and ranges. From heating issues to control malfunctions, we ensure safe and reliable cooking performance.</p>
                <div class="header-cta">
                    <a href="appliance_repair_schedule_service_v1.html" class="cta-button">Schedule Repair Service</a>
                    <a href="tel:+18035551234" class="cta-button cta-white">Call (*************</a>
                </div>
            </div>
        </section>

        <section class="service-overview">
            <div class="container">
                <div class="overview-grid">
                    <div class="overview-content">
                        <h2>Expert Oven & Range Repair Service</h2>
                        <p>Your oven and range are essential for preparing meals for your family. When they malfunction, it disrupts your entire routine. Our certified technicians provide comprehensive repair services for all types of cooking appliances.</p>
                        <p>We service both gas and electric models, from basic ranges to high-end professional-style units with advanced features.</p>
                        <ul class="overview-features">
                            <li>Gas and electric oven repair</li>
                            <li>Heating element replacement</li>
                            <li>Temperature calibration and control</li>
                            <li>Ignition system repair</li>
                            <li>Door and hinge adjustments</li>
                            <li>Safety inspections included</li>
                        </ul>
                    </div>
                    <div class="overview-image">🔥</div>
                </div>
            </div>
        </section>

        <section class="problems-section">
            <div class="container">
                <h2 class="section-title">Common Oven & Range Problems We Fix</h2>
                <div class="problems-grid">
                    <div class="problem-card">
                        <h4>Not Heating Properly</h4>
                        <p>Temperature issues affect cooking performance and food safety.</p>
                        <ul class="problem-symptoms">
                            <li>Oven not reaching set temperature</li>
                            <li>Uneven heating or hot spots</li>
                            <li>Takes too long to preheat</li>
                            <li>Temperature fluctuations</li>
                        </ul>
                    </div>

                    <div class="problem-card">
                        <h4>Heating Element Problems</h4>
                        <p>Faulty heating elements prevent proper oven operation.</p>
                        <ul class="problem-symptoms">
                            <li>Element not glowing red</li>
                            <li>Partial heating only</li>
                            <li>Visible damage to element</li>
                            <li>Sparking or arcing</li>
                        </ul>
                    </div>

                    <div class="problem-card">
                        <h4>Gas Ignition Issues</h4>
                        <p>Gas ovens and ranges require proper ignition for safe operation.</p>
                        <ul class="problem-symptoms">
                            <li>Burners won't light</li>
                            <li>Clicking but no ignition</li>
                            <li>Gas smell without flame</li>
                            <li>Pilot light problems</li>
                        </ul>
                    </div>

                    <div class="problem-card">
                        <h4>Control Panel Malfunctions</h4>
                        <p>Electronic controls are essential for modern oven operation.</p>
                        <ul class="problem-symptoms">
                            <li>Display not working</li>
                            <li>Buttons unresponsive</li>
                            <li>Timer not functioning</li>
                            <li>Error codes appearing</li>
                        </ul>
                    </div>

                    <div class="problem-card">
                        <h4>Door and Seal Issues</h4>
                        <p>Proper door sealing is crucial for efficient oven operation.</p>
                        <ul class="problem-symptoms">
                            <li>Door won't close properly</li>
                            <li>Heat escaping from door</li>
                            <li>Damaged door gasket</li>
                            <li>Hinge problems</li>
                        </ul>
                    </div>

                    <div class="problem-card">
                        <h4>Self-Cleaning Problems</h4>
                        <p>Self-cleaning cycle malfunctions can affect oven performance.</p>
                        <ul class="problem-symptoms">
                            <li>Cleaning cycle won't start</li>
                            <li>Door won't unlock after cleaning</li>
                            <li>Excessive smoke during cleaning</li>
                            <li>Cleaning cycle stops mid-way</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section class="emergency-cta">
            <div class="container">
                <h2>Oven or Range Not Working? Get Expert Help</h2>
                <p>Don't let cooking appliance problems disrupt your meal preparation. Contact Queen City Appliance Repair Service for professional oven and range repair service.</p>
                <a href="appliance_repair_schedule_service_v1.html" class="cta-button">Schedule Service Now</a>
                <a href="tel:+18035551234" class="cta-button cta-white">Call (*************</a>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Queen City Appliance Repair Service</h4>
                    <p>Expert Repairs, Reliable Service</p>
                    <p>Serving York, SC and the greater Charlotte area with professional appliance repair solutions.</p>
                </div>
                <div class="footer-section">
                    <h4>Our Services</h4>
                    <ul style="list-style: none;">
                        <li><a href="appliance_repair_refrigerator_repair_v1.html">Refrigerator Repair</a></li>
                        <li><a href="appliance_repair_dishwasher_repair_v1.html">Dishwasher Repair</a></li>
                        <li><a href="appliance_repair_oven_repair_v1.html">Oven & Range Repair</a></li>
                        <li><a href="appliance_repair_washing_machine_repair_v1.html">Washing Machine Repair</a></li>
                        <li><a href="appliance_repair_dryer_repair_v1.html">Dryer Repair</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Service Areas</h4>
                    <p><strong>South Carolina:</strong> Fort Mill, Tega Cay, Rock Hill, Lake Wylie, York, Clover, Newport</p>
                    <p><strong>North Carolina:</strong> Davidson, Weddington, Marvin, Huntersville, Cornelius, Waxhaw</p>
                </div>
                <div class="footer-section">
                    <h4>Contact Information</h4>
                    <p><strong>Phone:</strong> <a href="tel:+18035551234">(*************</a></p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <a href="appliance_repair_schedule_service_v1.html" class="cta-button" style="margin-top: 1rem; display: inline-block;">Schedule Service</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Queen City Appliance Repair Service. All rights reserved. | Professional appliance repair serving York, SC and Charlotte area.</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const ctaButtons = document.querySelectorAll('.cta-button');
            ctaButtons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('CTA clicked:', this.textContent.trim());
                });
            });
        });

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });
    </script>
</body>
</html>
