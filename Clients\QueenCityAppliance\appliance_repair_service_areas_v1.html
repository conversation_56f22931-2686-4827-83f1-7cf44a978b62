<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Areas | Queen City Appliance Repair Service | York, SC & Charlotte Area</title>
    <meta name="description" content="We provide professional appliance repair services throughout York, SC, Rock Hill, Fort Mill, Charlotte area, and surrounding communities in North and South Carolina.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --service-blue: #005A9C;
            --clean-white: #FFFFFF;
            --action-orange: #F68D2E;
            --light-gray: #F5F5F5;
            --charcoal: #333333;
            --success-green: #28A745;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: var(--charcoal);
            background-color: var(--clean-white);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background-color: var(--service-blue);
            color: var(--clean-white);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .phone-cta {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s ease;
            display: inline-block;
        }

        .phone-cta:hover {
            background-color: #e67c1a;
        }

        /* Navigation */
        nav {
            background-color: var(--light-gray);
            padding: 0.5rem 0;
        }

        .nav-links {
            display: flex;
            list-style: none;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
        }

        .nav-links a {
            color: var(--charcoal);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--service-blue);
        }

        .nav-links .active {
            color: var(--service-blue);
            font-weight: bold;
        }

        /* Page Header */
        .page-header {
            background: linear-gradient(135deg, var(--service-blue) 0%, #0066b3 100%);
            color: var(--clean-white);
            padding: 3rem 0;
            text-align: center;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .page-header p {
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Service Areas Section */
        .service-areas-section {
            padding: 4rem 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.2rem;
            margin-bottom: 3rem;
            color: var(--service-blue);
        }

        .areas-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .area-card {
            background-color: var(--clean-white);
            padding: 2.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid var(--light-gray);
            text-align: center;
        }

        .area-card h3 {
            color: var(--service-blue);
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .state-icon {
            font-size: 1.8rem;
        }

        .cities-list {
            list-style: none;
            text-align: left;
        }

        .cities-list li {
            padding: 0.8rem 0;
            border-bottom: 1px solid var(--light-gray);
            position: relative;
            padding-left: 2rem;
            font-size: 1.1rem;
        }

        .cities-list li:before {
            content: "📍";
            position: absolute;
            left: 0;
            color: var(--action-orange);
        }

        .cities-list li:last-child {
            border-bottom: none;
        }

        /* Map Section */
        .map-section {
            background-color: var(--light-gray);
            padding: 4rem 0;
            text-align: center;
        }

        .map-placeholder {
            background-color: var(--clean-white);
            border: 2px solid var(--service-blue);
            border-radius: 10px;
            padding: 4rem 2rem;
            margin: 2rem 0;
            color: var(--service-blue);
            font-size: 1.2rem;
        }

        /* Coverage Details */
        .coverage-section {
            padding: 4rem 0;
        }

        .coverage-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .coverage-item {
            text-align: center;
            padding: 2rem;
        }

        .coverage-icon {
            font-size: 3rem;
            color: var(--action-orange);
            margin-bottom: 1rem;
        }

        .coverage-item h4 {
            color: var(--service-blue);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        /* CTA Section */
        .cta-section {
            background-color: var(--service-blue);
            color: var(--clean-white);
            padding: 4rem 0;
            text-align: center;
        }

        .cta-section h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .cta-section p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-button {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-size: 1.1rem;
            font-weight: bold;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0 10px 10px 0;
            min-height: 44px;
            line-height: 1.2;
        }

        .cta-button:hover {
            background-color: #e67c1a;
            transform: translateY(-2px);
        }

        .cta-secondary {
            background-color: transparent;
            border: 2px solid var(--clean-white);
        }

        .cta-secondary:hover {
            background-color: var(--clean-white);
            color: var(--service-blue);
        }

        /* Footer */
        footer {
            background-color: var(--charcoal);
            color: var(--clean-white);
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h4 {
            color: var(--action-orange);
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: var(--clean-white);
            text-decoration: none;
        }

        .footer-section a:hover {
            color: var(--action-orange);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #555;
            font-size: 0.9rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                gap: 1rem;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .areas-grid {
                grid-template-columns: 1fr;
            }

            .area-card h3 {
                flex-direction: column;
                gap: 1rem;
            }

            .cta-button {
                display: block;
                margin: 10px auto;
                text-align: center;
            }
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles */
        a:focus, button:focus {
            outline: 2px solid var(--action-orange);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">Queen City Appliance Repair Service</div>
                <a href="tel:+18035551234" class="phone-cta" aria-label="Call us at ************">📞 (*************</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul class="nav-links">
                <li><a href="appliance_repair_homepage_v1.html">Home</a></li>
                <li><a href="appliance_repair_services_hub_v1.html">Services</a></li>
                <li><a href="#service-areas" class="active">Service Areas</a></li>
                <li><a href="appliance_repair_schedule_service_v1.html">Schedule Service</a></li>
                <li><a href="appliance_repair_why_choose_us_v1.html">Why Choose Us</a></li>
                <li><a href="appliance_repair_contact_us_v1.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section class="page-header">
            <div class="container">
                <h1>Service Areas</h1>
                <p>Proudly serving communities throughout York, SC, and the greater Charlotte area with professional appliance repair services.</p>
            </div>
        </section>

        <section class="service-areas-section" id="service-areas">
            <div class="container">
                <h2 class="section-title">Communities We Serve</h2>
                <div class="areas-grid">
                    <div class="area-card">
                        <h3><span class="state-icon">🏛️</span>South Carolina</h3>
                        <ul class="cities-list">
                            <li>Fort Mill</li>
                            <li>Tega Cay</li>
                            <li>Rock Hill</li>
                            <li>Lake Wylie</li>
                            <li>York</li>
                            <li>Clover</li>
                            <li>Newport</li>
                        </ul>
                    </div>
                    <div class="area-card">
                        <h3><span class="state-icon">🏔️</span>North Carolina</h3>
                        <ul class="cities-list">
                            <li>Davidson</li>
                            <li>Weddington</li>
                            <li>Marvin</li>
                            <li>Huntersville</li>
                            <li>Cornelius</li>
                            <li>Waxhaw</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section class="map-section">
            <div class="container">
                <h2 class="section-title">Our Service Area Map</h2>
                <div class="map-placeholder">
                    <p><strong>Interactive Service Area Map</strong></p>
                    <p>📍 Covering a 30-mile radius from York, SC</p>
                    <p>🚗 Fast response times throughout our service area</p>
                    <p>📞 Call (************* to confirm service availability in your area</p>
                </div>
                <p style="margin-top: 1rem; font-style: italic;">Don't see your city listed? Contact us to check if we service your area.</p>
            </div>
        </section>

        <section class="coverage-section">
            <div class="container">
                <h2 class="section-title">Complete Coverage Details</h2>
                <div class="coverage-grid">
                    <div class="coverage-item">
                        <div class="coverage-icon">🚗</div>
                        <h4>Fast Response Times</h4>
                        <p>Same-day and next-day service available throughout our coverage area. Emergency repairs prioritized.</p>
                    </div>
                    <div class="coverage-item">
                        <div class="coverage-icon">📍</div>
                        <h4>Local Expertise</h4>
                        <p>Deep knowledge of the York, SC and Charlotte area communities. We understand local needs and preferences.</p>
                    </div>
                    <div class="coverage-item">
                        <div class="coverage-icon">🛣️</div>
                        <h4>No Travel Charges</h4>
                        <p>Standard service calls include travel within our primary service area. Transparent pricing with no hidden fees.</p>
                    </div>
                    <div class="coverage-item">
                        <div class="coverage-icon">⏰</div>
                        <h4>Flexible Scheduling</h4>
                        <p>Morning, afternoon, and evening appointments available. Weekend service for urgent repairs.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="cta-section">
            <div class="container">
                <h2>Ready to Schedule Your Appliance Repair?</h2>
                <p>Contact us today to schedule professional appliance repair service in your area. Fast, reliable service you can trust.</p>
                <a href="appliance_repair_schedule_service_v1.html" class="cta-button">Schedule Service Online</a>
                <a href="tel:+18035551234" class="cta-button cta-secondary">Call (*************</a>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Queen City Appliance Repair Service</h4>
                    <p>Expert Repairs, Reliable Service</p>
                    <p>Serving York, SC and the greater Charlotte area with professional appliance repair solutions.</p>
                </div>
                <div class="footer-section">
                    <h4>Our Services</h4>
                    <ul style="list-style: none;">
                        <li><a href="appliance_repair_refrigerator_repair_v1.html">Refrigerator Repair</a></li>
                        <li><a href="appliance_repair_dishwasher_repair_v1.html">Dishwasher Repair</a></li>
                        <li><a href="appliance_repair_oven_repair_v1.html">Oven & Range Repair</a></li>
                        <li><a href="appliance_repair_washing_machine_repair_v1.html">Washing Machine Repair</a></li>
                        <li><a href="appliance_repair_dryer_repair_v1.html">Dryer Repair</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Service Areas</h4>
                    <p><strong>South Carolina:</strong> Fort Mill, Tega Cay, Rock Hill, Lake Wylie, York, Clover, Newport</p>
                    <p><strong>North Carolina:</strong> Davidson, Weddington, Marvin, Huntersville, Cornelius, Waxhaw</p>
                </div>
                <div class="footer-section">
                    <h4>Contact Information</h4>
                    <p><strong>Phone:</strong> <a href="tel:+18035551234">(*************</a></p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <a href="appliance_repair_schedule_service_v1.html" class="cta-button" style="margin-top: 1rem; display: inline-block;">Schedule Service</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Queen City Appliance Repair Service. All rights reserved. | Professional appliance repair serving York, SC and Charlotte area.</p>
            </div>
        </div>
    </footer>

    <script>
        // Add smooth scrolling for anchor links
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add click tracking for CTA buttons
            const ctaButtons = document.querySelectorAll('.cta-button');
            ctaButtons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('CTA clicked:', this.textContent.trim());
                });
            });
        });

        // Add keyboard navigation support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });
    </script>
</body>
</html>
