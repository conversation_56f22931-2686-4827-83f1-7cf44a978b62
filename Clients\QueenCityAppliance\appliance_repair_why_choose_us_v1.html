<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Why Choose Us | Queen City Appliance Repair Service | York, SC & Charlotte</title>
    <meta name="description" content="Discover why customers choose Queen City Appliance Repair Service for professional appliance repair in York, SC and Charlotte area. Expert technicians, quality service, and customer satisfaction guaranteed.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --service-blue: #005A9C;
            --clean-white: #FFFFFF;
            --action-orange: #F68D2E;
            --light-gray: #F5F5F5;
            --charcoal: #333333;
            --success-green: #28A745;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: var(--charcoal);
            background-color: var(--clean-white);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background-color: var(--service-blue);
            color: var(--clean-white);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .phone-cta {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s ease;
            display: inline-block;
        }

        .phone-cta:hover {
            background-color: #e67c1a;
        }

        /* Navigation */
        nav {
            background-color: var(--light-gray);
            padding: 0.5rem 0;
        }

        .nav-links {
            display: flex;
            list-style: none;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
        }

        .nav-links a {
            color: var(--charcoal);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--service-blue);
        }

        .nav-links .active {
            color: var(--service-blue);
            font-weight: bold;
        }

        /* Page Header */
        .page-header {
            background: linear-gradient(135deg, var(--service-blue) 0%, #0066b3 100%);
            color: var(--clean-white);
            padding: 3rem 0;
            text-align: center;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .page-header p {
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Why Choose Us Section */
        .why-choose-section {
            padding: 4rem 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.2rem;
            margin-bottom: 3rem;
            color: var(--service-blue);
        }

        .reasons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .reason-card {
            background-color: var(--light-gray);
            padding: 2.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .reason-card:hover {
            transform: translateY(-5px);
        }

        .reason-icon {
            font-size: 4rem;
            color: var(--action-orange);
            margin-bottom: 1.5rem;
        }

        .reason-card h3 {
            color: var(--service-blue);
            font-size: 1.4rem;
            margin-bottom: 1rem;
        }

        .reason-card p {
            font-size: 1.1rem;
            line-height: 1.6;
        }

        /* Process Section */
        .process-section {
            background-color: var(--light-gray);
            padding: 4rem 0;
        }

        .process-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .process-step {
            text-align: center;
            padding: 2rem;
            background-color: var(--clean-white);
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .step-number {
            background-color: var(--action-orange);
            color: var(--clean-white);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0 auto 1rem;
        }

        .process-step h4 {
            color: var(--service-blue);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        /* Stats Section */
        .stats-section {
            padding: 4rem 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            text-align: center;
        }

        .stat-item {
            padding: 2rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: var(--action-orange);
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 1.2rem;
            color: var(--service-blue);
            font-weight: bold;
        }

        .stat-description {
            font-size: 1rem;
            color: var(--charcoal);
            margin-top: 0.5rem;
        }

        /* CTA Section */
        .cta-section {
            background-color: var(--service-blue);
            color: var(--clean-white);
            padding: 4rem 0;
            text-align: center;
        }

        .cta-section h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .cta-section p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-button {
            background-color: var(--action-orange);
            color: var(--clean-white);
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-size: 1.1rem;
            font-weight: bold;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0 10px 10px 0;
            min-height: 44px;
            line-height: 1.2;
        }

        .cta-button:hover {
            background-color: #e67c1a;
            transform: translateY(-2px);
        }

        .cta-secondary {
            background-color: transparent;
            border: 2px solid var(--clean-white);
        }

        .cta-secondary:hover {
            background-color: var(--clean-white);
            color: var(--service-blue);
        }

        /* Footer */
        footer {
            background-color: var(--charcoal);
            color: var(--clean-white);
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h4 {
            color: var(--action-orange);
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: var(--clean-white);
            text-decoration: none;
        }

        .footer-section a:hover {
            color: var(--action-orange);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #555;
            font-size: 0.9rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                gap: 1rem;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .reasons-grid {
                grid-template-columns: 1fr;
            }

            .cta-button {
                display: block;
                margin: 10px auto;
                text-align: center;
            }
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles */
        a:focus, button:focus {
            outline: 2px solid var(--action-orange);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">Queen City Appliance Repair Service</div>
                <a href="tel:+18035551234" class="phone-cta" aria-label="Call us at ************">📞 (*************</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul class="nav-links">
                <li><a href="appliance_repair_homepage_v1.html">Home</a></li>
                <li><a href="appliance_repair_services_hub_v1.html">Services</a></li>
                <li><a href="appliance_repair_service_areas_v1.html">Service Areas</a></li>
                <li><a href="appliance_repair_schedule_service_v1.html">Schedule Service</a></li>
                <li><a href="#why-choose" class="active">Why Choose Us</a></li>
                <li><a href="appliance_repair_contact_us_v1.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section class="page-header">
            <div class="container">
                <h1>Why Choose Queen City Appliance Repair Service?</h1>
                <p>Discover what sets us apart as the trusted choice for appliance repair in York, SC and the greater Charlotte area.</p>
            </div>
        </section>

        <section class="why-choose-section" id="why-choose">
            <div class="container">
                <h2 class="section-title">What Makes Us Different</h2>
                <div class="reasons-grid">
                    <div class="reason-card">
                        <div class="reason-icon">🔧</div>
                        <h3>Expert Technicians</h3>
                        <p>Our certified technicians have years of experience repairing all major appliance brands. We stay current with the latest technology and repair techniques to provide the best service possible.</p>
                    </div>

                    <div class="reason-card">
                        <div class="reason-icon">⚡</div>
                        <h3>Fast Response Times</h3>
                        <p>We understand that appliance problems can't wait. That's why we offer same-day and next-day service appointments, with emergency service available when you need it most.</p>
                    </div>

                    <div class="reason-card">
                        <div class="reason-icon">💰</div>
                        <h3>Transparent Pricing</h3>
                        <p>No surprises or hidden fees. We provide upfront pricing before any work begins, so you know exactly what to expect. Our competitive rates offer excellent value for professional service.</p>
                    </div>

                    <div class="reason-card">
                        <div class="reason-icon">🛡️</div>
                        <h3>90-Day Warranty</h3>
                        <p>We stand behind our work with a comprehensive 90-day warranty on all repairs. If you experience any issues with our service, we'll make it right at no additional cost.</p>
                    </div>

                    <div class="reason-card">
                        <div class="reason-icon">🏠</div>
                        <h3>Local & Trusted</h3>
                        <p>As a local business serving York, SC and the Charlotte area, we're committed to our community. We've built our reputation on reliability, honesty, and exceptional customer service.</p>
                    </div>

                    <div class="reason-card">
                        <div class="reason-icon">🔍</div>
                        <h3>Thorough Diagnosis</h3>
                        <p>We don't just fix symptoms – we identify and address the root cause of appliance problems. Our comprehensive diagnostic process ensures lasting repairs and prevents future issues.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="process-section">
            <div class="container">
                <h2 class="section-title">Our Service Process</h2>
                <div class="process-steps">
                    <div class="process-step">
                        <div class="step-number">1</div>
                        <h4>Contact Us</h4>
                        <p>Call us or schedule online. We'll gather information about your appliance issue and schedule a convenient appointment time.</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">2</div>
                        <h4>Professional Diagnosis</h4>
                        <p>Our expert technician arrives on time and performs a thorough diagnosis to identify the exact problem with your appliance.</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">3</div>
                        <h4>Upfront Pricing</h4>
                        <p>We provide clear, upfront pricing for the repair before any work begins. No hidden fees or surprise charges.</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">4</div>
                        <h4>Expert Repair</h4>
                        <p>Using genuine parts and professional techniques, we complete the repair efficiently and test to ensure everything works perfectly.</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">5</div>
                        <h4>Quality Guarantee</h4>
                        <p>We clean up our work area and provide you with a 90-day warranty on the repair, giving you peace of mind.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="stats-section">
            <div class="container">
                <h2 class="section-title">Our Track Record</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">15+</span>
                        <div class="stat-label">Years of Experience</div>
                        <div class="stat-description">Serving the community with reliable appliance repair expertise</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1000+</span>
                        <div class="stat-label">Satisfied Customers</div>
                        <div class="stat-description">Trusted by families throughout York, SC and Charlotte area</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">95%</span>
                        <div class="stat-label">First-Visit Fix Rate</div>
                        <div class="stat-description">Most repairs completed on the first visit</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">24/7</span>
                        <div class="stat-label">Emergency Service</div>
                        <div class="stat-description">Available when you need us most for urgent repairs</div>
                    </div>
                </div>
            </div>
        </section>

        <section class="cta-section">
            <div class="container">
                <h2>Experience the Queen City Difference</h2>
                <p>Join thousands of satisfied customers who trust Queen City Appliance Repair Service for reliable, professional appliance repair. Contact us today to schedule your service.</p>
                <a href="appliance_repair_schedule_service_v1.html" class="cta-button">Schedule Service Now</a>
                <a href="tel:+18035551234" class="cta-button cta-secondary">Call (*************</a>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Queen City Appliance Repair Service</h4>
                    <p>Expert Repairs, Reliable Service</p>
                    <p>Serving York, SC and the greater Charlotte area with professional appliance repair solutions.</p>
                </div>
                <div class="footer-section">
                    <h4>Our Services</h4>
                    <ul style="list-style: none;">
                        <li><a href="appliance_repair_refrigerator_repair_v1.html">Refrigerator Repair</a></li>
                        <li><a href="appliance_repair_dishwasher_repair_v1.html">Dishwasher Repair</a></li>
                        <li><a href="appliance_repair_oven_repair_v1.html">Oven & Range Repair</a></li>
                        <li><a href="appliance_repair_washing_machine_repair_v1.html">Washing Machine Repair</a></li>
                        <li><a href="appliance_repair_dryer_repair_v1.html">Dryer Repair</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Service Areas</h4>
                    <p><strong>South Carolina:</strong> Fort Mill, Tega Cay, Rock Hill, Lake Wylie, York, Clover, Newport</p>
                    <p><strong>North Carolina:</strong> Davidson, Weddington, Marvin, Huntersville, Cornelius, Waxhaw</p>
                </div>
                <div class="footer-section">
                    <h4>Contact Information</h4>
                    <p><strong>Phone:</strong> <a href="tel:+18035551234">(*************</a></p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <a href="appliance_repair_schedule_service_v1.html" class="cta-button" style="margin-top: 1rem; display: inline-block;">Schedule Service</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Queen City Appliance Repair Service. All rights reserved. | Professional appliance repair serving York, SC and Charlotte area.</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add click tracking for CTA buttons
            const ctaButtons = document.querySelectorAll('.cta-button');
            ctaButtons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('CTA clicked:', this.textContent.trim());
                });
            });

            // Add smooth scrolling for anchor links
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // Add keyboard navigation support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });
    </script>
</body>
</html>
